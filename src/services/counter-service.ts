import {
  doc,
  runTransaction,
  serverTimestamp,
} from 'firebase/firestore';

import { COUNTERS_COLLECTION_NAME } from '@/constants/core.constants';
import { firestore } from '@/root-context';

export interface CounterEntity {
  id: string;
  value: number;
  updatedAt: Date;
}

export async function getNextCounterValue(
  counterName: string,
): Promise<number> {
  const counterRef = doc(firestore, COUNTERS_COLLECTION_NAME, counterName);

  try {
    const nextValue = await runTransaction(firestore, async (transaction) => {
      const counterDoc = await transaction.get(counterRef);

      let currentValue = 0;
      if (counterDoc.exists()) {
        const counterData = counterDoc.data() as CounterEntity;
        currentValue = counterData.value;
      }

      const nextValue = currentValue + 1;

      transaction.set(
        counterRef,
        {
          id: counterName,
          value: nextValue,
          updatedAt: serverTimestamp(),
        },
        { merge: true },
      );

      return nextValue;
    });

    return nextValue;
  } catch (error) {
    console.error(
      `Error getting next counter value for ${counterName}:`,
      error,
    );
    throw error;
  }
}
